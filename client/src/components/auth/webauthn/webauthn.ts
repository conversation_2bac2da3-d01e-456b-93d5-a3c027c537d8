// webauthn.ts
import {
    startRegistration,
    startAuthentication,
    browserSupportsWebAuthn,
} from '@simplewebauthn/browser';

import {useLogins} from 'stores/logins';
import {SessionStorage} from 'symbol-auth-client';
import {useAuth} from 'stores/auth';

export function isWebAuthnSupported() {
    return browserSupportsWebAuthn();
}

/**
 * Register a new passkey for a user.
 * Calls your `webauthn` service over the socket:
 *  - create({ action: 'registrationOptions', ... })
 *  - startRegistration(options)
 *  - create({ action: 'registrationVerify', ... })
 */
export async function registerPasskey(
    opts: { login: any; usernameField?: string; displayName?: string }
): Promise<{ success: boolean; message: string, login: any }> {
    if (!browserSupportsWebAuthn()) {
        return {login: undefined, success: false, message: 'Your browser does not support WebAuthn'};
    }

    try {
        const rpID = SessionStorage.getItem('fqdn')
        const args = [{loginAttempts: {$push: {$each: [new Date()], $position: 0, $slice: 20}}}, {
            query: {
                loginOptions: {
                    usernameField: opts.usernameField,
                    rpID,
                    method: 'webauthn',
                    authAction: 'register'
                }
            }
        }]
        let method = 'create';
        if (opts.login) {
            method = 'patch';
            args.unshift(opts.login._id as any);
        }
        // 1) Get options from server
        const login: any = await useLogins()[method](...args)

        // 2) Browser ceremony
        const credential = await startRegistration(login._fastjoin.webauthnOptions);

        // 3) Verify & persist authenticator on server
        await useAuth().authenticate({
            strategy: 'webauthn',
            credential,
            register: true,
            rpID
        } as any)
            .catch(err => {
                console.error(`Error registering passkey: ${err.message}`)
                return {login: undefined, success: false, message: `Failed to register passkey: ${err.message}`};
            })

        return {login, success: true, message: 'Passkey created'};
    } catch (err: any) {
        // Common: NotAllowedError if user cancels, InvalidStateError if already registered, etc.
        return {login: undefined, success: false, message: err?.message ?? 'Registration error'};
    }
}

/**
 * Authenticate with a passkey.
 *  - create({ action: 'authenticationOptions', ... })
 *  - startAuthentication(options)
 *  - authentication.create({ strategy: 'webauthn', credential })
 *
 * If you omit loginId, this will attempt **usernameless** sign-in.
 */
export async function loginWithPasskey(
    opts: {
        login: any,
        usernameField?: string
    },
): Promise<{ login: any, success: boolean; message: string; authResult?: any }> {
    if (!browserSupportsWebAuthn()) {
        return {login: undefined, success: false, message: 'Your browser does not support WebAuthn'};
    }

    try {
        const rpID = SessionStorage.getItem('fqdn')
        // 1) Get options (loginId optional for usernameless)
        const login: any = await useLogins().patch(opts.login._id, {
            loginAttempts: {
                $push: {
                    $each: [new Date()],
                    $position: 0,
                    $slice: 20
                }
            }
        }, {
            query: {
                loginOptions: {
                    usernameField: opts.usernameField,
                    rpID,
                    method: 'webauthn',
                    authAction: 'authenticate'
                    // Note: credential is NOT passed here - it will be generated by the browser
                    // and sent later during the authentication verification step
                }
            }
        });

        // 2) Browser ceremony
        const credential = await startAuthentication(login._fastjoin.webauthnOptions);

        // 3) Ask Feathers to issue JWT via your `webauthn` strategy
        await useAuth().authenticate({
            strategy: 'webauthn',
            credential,
            register: false,
            rpID
        } as any)
            .catch(err => {
                console.error(`Error authenticating passkey: ${err.message}`)
                return {login: undefined, success: false, message: `Failed to authenticate passkey: ${err.message}`};
            })

        return {success: true, message: 'Signed in', login};
    } catch (err: any) {
        return {login: undefined, success: false, message: err?.message ?? 'Authentication error'};
    }
}
