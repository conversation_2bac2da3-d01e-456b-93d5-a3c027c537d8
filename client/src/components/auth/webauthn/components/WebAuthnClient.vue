<template>
  <div class="_fw">
    <email-field
        @focus="setFocus"
        @blur="setBlur"
        v-model="form.email"
        input-class="font-3-4r"
        placeholder="Email"
        dense
        hide-bottom-space
        @update:valid="setValid"
    ></email-field>

    <q-separator>
      <div v-if="v" class="_fw q-py-lg">
        <template v-if="loggingIn">
          <q-chip color="transparent">
            <q-spinner color="primary"></q-spinner>
            <span class="q-ml-sm">{{log}}</span>
          </q-chip>
        </template>
        <div class="q-pa-sm font-1r text-secondary" v-else-if="error">{{ error }}</div>
        <template v-else>
          <q-chip color="transparent" v-if="loginLoading">
            <q-spinner color="primary"></q-spinner>
            <span class="q-ml-sm">Searching for existing account</span>
          </q-chip>
          <template v-if="existing">
            <q-chip color="transparent">
              <q-icon color="green" name="mdi-check-circle"></q-icon>
              <span class="q-ml-sm">Account Found</span>
            </q-chip>
            <q-chip v-if="loadingKeys" color="transparent">
              <q-spinner color="accent"></q-spinner>
              <span class="q-ml-sm">Checking device keychain</span>
            </q-chip>
            <q-chip color="transparent" v-else-if="!keys.length" @click="createKey">
              <q-icon color="accent" name="mdi-key"></q-icon>
              <span class="q-ml-sm">Login using device security</span>
            </q-chip>
            <q-chip v-else color="transparent" clickable @click="loginWithKey">
              <q-icon color="primary" name="mdi-key-arrow-right"></q-icon>
              <span class="q-ml-sm">Login using device security</span>
            </q-chip>
          </template>
          <q-chip color="transparent" v-else @click="createKey">
            <q-icon color="accent" name="mdi-key"></q-icon>
            <span class="q-ml-sm">Login using device security</span>
          </q-chip>
        </template>
      </div>
    </q-separator>
  </div>
</template>

<script setup>
  import EmailField from 'components/common/input/EmailField.vue';

  import {computed, ref, watch} from 'vue';
  import {isWebAuthnSupported} from 'components/auth/webauthn/webauthn';
  import {usePasskeys} from 'stores/passkeys';
  import {useLogins} from 'stores/logins';
  import {LocalStorage, SessionStorage} from 'symbol-auth-client';

  const keyStore = usePasskeys();
  const loginStore = useLogins();

  const emit = defineEmits(['update:on', 'focus', 'blur']);
  const props = defineProps({
    on: Boolean,
    type: { type: String, default: 'email' }
  })

  const focusOn = ref(false);
  const matchTotal = ref(0);
  const existing = ref(undefined);
  const loadingKeys = ref(false);
  const loginLoading = ref(false);
  const loggingIn = ref(false);
  const log = ref('')
  const error = ref('');
  const keys = ref([])
  const v = ref(false);

  let settingFocus = ref(false);
  const setFocus = () => {
    settingFocus.value = true;
    focusOn.value = true;
    emit('focus');
    setTimeout(() => {
      settingFocus.value = false;
    }, 500);
  };
  const setBlur = () => {
    setTimeout(() => {
      if (!settingFocus.value) {
        focusOn.value = false;
        emit('blur');
      }
    }, 250);
  };

  const formDef = (defs) => {
    return {
      email: '',
      ...defs
    };
  };
  const form = ref(formDef());

  const preRun = async () => {
    const rpID = SessionStorage.getItem('fqdn')
    loggingIn.value = true;
    if(!existing.value){
      const newLogin = await loginStore.create(form.value, { query: { loginOptions: { method: 'webauthn', rpID }}})
          .catch(err => {
            console.error(`Error creating login: ${err.message}`)
            error.value = `Failed to create login: ${err.message}`;
          })
      if(newLogin) existing.value = newLogin;
      else {
        loggingIn.value = false;
        return;
      }
    }
    LocalStorage.setItem('ucan_aud', existing.value.did);
    LocalStorage.setItem('client_ucan', existing.value.ucan);
    SessionStorage.setItem('client_ucan', existing.value.ucan);

    if(!keys.value.length){
    }
  };
    if(!existing.value._fastjoin?.webAuthnOptions){
      const newLogin = await loginStore.patch(existing.value._id, { loginAttempts: {$push: {$each: [new Date()], $position: 0, $slice: 20}} }, { query: { loginOptions: { method: 'webauthn', rpID }}})
          .catch(err => {
            console.error(`Error creating login: ${err.message}`)
            error.value = `Failed to create login: ${err.message}`;
          })
      if(newLogin) existing.value = newLogin;
      else {
        loggingIn.value = false;
        return
  }

  let lastValid = ref('');
  const setValid = async (val) => {
    v.value = val;
    if (val) {
      setTimeout(async () => {
        const check = form.value[props.type];
        if (check !== lastValid.value) {
          lastValid.value = check;
          const query = { $limit: 1, [props.type]: check };
          loginLoading.value = true;
          const { total, data } = await loginStore.find({ query, loginOptions: { existCheck: true } })
              .catch(err => {
                console.error(`Error checking login: ${err.message}`)
                error.value = 'Failed to check for an existing login';
              });
          loginLoading.value = false;
          matchTotal.value = total;
          existing.value = data[0];
          if (total) {
            loadingKeys.value = true;
            const keyFetch = await keyStore.find({ query: { $limit: 1, login: existing.value._id } })
                .catch(err => {
                  console.error(`Error fetching keys: ${err.message}`)
                  error.value = 'Failed to search for validation keys';
                });
            loadingKeys.value = false;
            if (keyFetch.data) keys.value = keyFetch.data;
          }
        }
      }, 20);
    }
  };

  const isWebAuthn = computed(() => isWebAuthnSupported());
  watch(isWebAuthn, (nv) => {
    emit('update:on', !!nv);
  }, { immediate: true })
</script>

<style lang="scss" scoped>

</style>
